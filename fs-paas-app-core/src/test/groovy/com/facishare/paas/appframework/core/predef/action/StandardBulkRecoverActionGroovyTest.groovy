import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ActionContext
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * Created by fengjy in 2019/11/26 16:57
 */
class StandardBulkRecoverActionGroovyTest extends Specification {
    def bulkRecoverAction = new StandardBulkRecoverAction(
            serviceFacade: Mock(ServiceFacade),
            actionContext: new ActionContext(RequestContext.builder()
                    .tenantId("71698")
                    .user(new User("testTenantId", User.SUPPER_ADMIN_USER_ID))
                    .build(), "testObjectApiName", "testBulkDelete"),
            objectDescribe: getObjectDescribe()
    )

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    def "test before"() {
        given:
        def arg = getArg()
        bulkRecoverAction.arg = arg
        bulkRecoverAction.actionContext.attributes.put(RequestContext.Attributes.SKIP_BASE_VALIDATE, Boolean.TRUE)
        bulkRecoverAction.actionContext.attributes.put(RequestContext.Attributes.SKIP_FUNCTION_ACTION, Boolean.TRUE)
        when:
        bulkRecoverAction.before(arg)
        then:
        0 * bulkRecoverAction.serviceFacade.findObject(_, _) >> getObjectDescribe()
        0 * bulkRecoverAction.serviceFacade.findObjectDataByIds(*_)
        noExceptionThrown()
    }


    def "test doAct"() {
        given:
        def arg = getArg()
        when:
        bulkRecoverAction.doAct(arg)
        then:
        1 * bulkRecoverAction.serviceFacade.bulkRecover(*_) >> [getObjectData()]
        noExceptionThrown()
    }

    def "test dealMasterData"() {
        given:
        bulkRecoverAction.objectDescribe = getDetailDescribe()
        when:
        bulkRecoverAction.dealMasterData(getArg())
        then:
        1 * bulkRecoverAction.serviceFacade.findObjectDataByIdsIncludeDeletedIgnoreFormula(*_) >> [getObjectData()]
        1 * bulkRecoverAction.serviceFacade.findObjectDataByIdsIncludeDeletedIgnoreFormula(*_) >> [getDeleteObjectData()]

        thrown(ValidateException)
    }

    def "test dealOneDetail"() {
        given:
        println("given")
        when:
        bulkRecoverAction.dealOneDetail(getDetailDescribe(), [getObjectData()])
        then:
        1 * bulkRecoverAction.serviceFacade.findDetailObjectDataBatchWithPageIncludeInvalid(*_) >> new QueryResult<IObjectData>(
                data: [getObjectData()],
                totalNumber: 1001
        )
        0 * bulkRecoverAction.serviceFacade.bulkRecover(*_) >> [getObjectData()]
        1 * bulkRecoverAction.serviceFacade.findDetailObjectDataBatchWithPageIncludeInvalid(*_) >> new QueryResult<IObjectData>(
                data: [],
                totalNumber: 0
        )
        noExceptionThrown()
    }

    def "test dealWithDetail"() {
        when:
        bulkRecoverAction.dealWithDetail([getObjectData()])
        then:
        1 * bulkRecoverAction.serviceFacade.findDetailDescribes(*_) >> [getObjectDescribe()]
        noExceptionThrown()
    }

    def "test after"() {
        given:
        bulkRecoverAction.actionListeners = []
        when:
        bulkRecoverAction.after(getArg(), StandardBulkRecoverAction.Result.builder().build())
        then:
        noExceptionThrown()
    }

    ObjectData getObjectData() {
        new ObjectData([
                "is_active"               : Boolean.TRUE,
                "api_name"                : "testAPIName",
                "_id"                     : "testObjectId",
                "name"                    : "testObjectName",
                "object_describe_api_name": "testDescribeAPIName",
                "testFieldAPIName"        : "123"
        ])
    }

    ObjectData getDeleteObjectData() {
        ObjectData objectData = getObjectData()
        objectData.set("is_deleted", Boolean.TRUE)
        return objectData
    }

    ObjectDescribe getObjectDescribe() {
        new ObjectDescribe(["is_active": Boolean.TRUE, "api_name": "testDescribeAPIName"])
    }


    ObjectDescribe getDetailDescribe() {
        ObjectDescribe detailDescribe = getObjectDescribe()
        detailDescribe.setFieldDescribes([new MasterDetailFieldDescribe(
                "api_name": "testFieldAPIName"
        )])
        return detailDescribe
    }

    StandardBulkRecoverAction.Arg getArg() {
        new StandardBulkRecoverAction.Arg(
                idList: ["testId"],
                objectDescribeAPIName: "testDescribeApiName"
        )
    }
}

package com.facishare.paas.appframework.core.predef.action


import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.data.IUniqueRule
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe
import com.fxiaoke.functions.utils.Lists
import com.google.common.collect.Maps
import spock.lang.Specification

class StandardUpdateImportVerifyActionGroovyTest extends Specification {

    def objectApiName = "object_123__c"

    def actionCode = "UnionInsertImportData"

    def tenantId = "74255"

    def userId = "1000"

    def outTenantId = "200074255"

    def outUserId = "100018916"

    def user = User.builder().tenantId(tenantId).userId(userId).outTenantId(outTenantId).outUserId(outUserId).build()

    def requestContext = RequestContext.builder().tenantId(tenantId).user(user).build()

    def actionContext = new ActionContext(requestContext, objectApiName, actionCode)

    ServiceFacade serviceFacade
    InfraServiceFacade infraServiceFacade
    IObjectDescribe objectDescribe
    SpringBeanHolder springBeanHolder

    def setup() {
        serviceFacade = Mock()
        infraServiceFacade = Mock()
        objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        objectDescribe.setIsActive(true)
        springBeanHolder = Mock(SpringBeanHolder)
        infraServiceFacade.getSpringBeanHolder() >> springBeanHolder
    }

    def "test getFuncPrivilegeCodes"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                updateOwner: true
        )

        StandardUpdateImportVerifyAction action = new StandardUpdateImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                arg: arg
        )
        when:
        action.getFuncPrivilegeCodes()
        then:
        noExceptionThrown()
    }

    def "test verifyChangeOrderObject"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)

        StandardUpdateImportVerifyAction action = new StandardUpdateImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade
        )
        when:
        action.verifyChangeOrderObject()
        then:
        noExceptionThrown()
    }

    def "test recordLog"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        objectDescribe.setDisplayName("unit")
        StandardUpdateImportVerifyAction action = new StandardUpdateImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade
        )
        when:
        action.recordLog()
        then:
        noExceptionThrown()
    }

    def "test getValidImportFields"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)

        ObjectDataDocument dataDocument = new ObjectDataDocument()
        dataDocument.put("name", "1")

        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        fieldDescribe.setApiName("name")

        objectDescribe.setFieldDescribes(Lists.newArrayList(fieldDescribe))

        Map<String, List<BaseImportAction.FieldMapping>> fieldMappingMap = Maps.newHashMap()
        List<BaseImportAction.FieldMapping> fieldMappingList = Lists.newArrayList()
        BaseImportAction.FieldMapping fieldMapping = new BaseImportAction.FieldMapping()
        fieldMapping.setApiName(objectApiName)
        fieldMapping.setColIndex("1")
        fieldMappingList.add(fieldMapping)
        fieldMappingMap.put(objectApiName, fieldMappingList)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                supportFieldMapping: supportFieldMapping,
                rows: [dataDocument],
                matchingType: matchType
        )

        StandardUpdateImportVerifyAction action = new StandardUpdateImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                infraServiceFacade: infraServiceFacade,
                fieldMappings: fieldMappingMap,
                arg: arg
        )
        when:
        infraServiceFacade.getUpdateImportTemplateField(actionContext.getUser(), objectDescribe) >> [fieldDescribe]
        action.getValidImportFields()
        then:
        noExceptionThrown()
        where:
        supportFieldMapping | matchType
        true                | 1
        true                | 2
        false               | 1
        false               | 2
    }

    def "test handelUniqueRuleFields"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)

        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        String fieldApiName = "field_text__c"
        fieldDescribe.setApiName(fieldApiName)


        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                matchingType: matchType
        )

        StandardUpdateImportVerifyAction action = new StandardUpdateImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                arg: arg,
                uniqueRule: uniqueRule
        )
        when:
        action.handelUniqueRuleFields(Lists.newArrayList(fieldDescribe))
        then:
        noExceptionThrown()
        where:
        matchType | uniqueRule
        3         | new IUniqueRule()
        3         | null
    }

    def "test validateMatchTypeField"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)

        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        String fieldApiName = "field_text__c"
        fieldDescribe.setApiName(fieldApiName)

        List<IFieldDescribe> validFieldList = Lists.newArrayList(fieldDescribe)

        objectDescribe.setFieldDescribes(validFieldList)

        Map<String, Object> data = Maps.newHashMap()
        data.put(fieldApiName, "test")

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                matchingType: matchType
        )

        StandardUpdateImportVerifyAction action = new StandardUpdateImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                arg: arg
        )
        when:
        action.validateMatchTypeField(matchTypeField, data.entrySet())
        then:
        noExceptionThrown()
        where:
        matchType | matchTypeField
        1         | "field_text__c"
        2         | "field_text__c"
    }

    def "test validateMatchTypeField2"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)

        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        String fieldApiName = "field_text__c"
        fieldDescribe.setApiName(fieldApiName)

        List<IFieldDescribe> validFieldList = Lists.newArrayList(fieldDescribe)

        objectDescribe.setFieldDescribes(validFieldList)

        IObjectData data = new ObjectData()
        data.set(fieldApiName, "test")

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                matchingType: matchType
        )

        StandardUpdateImportVerifyAction action = new StandardUpdateImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                arg: arg
        )
        when:
        action.validateMatchTypeField(matchTypeField, data)
        then:
        noExceptionThrown()
        where:
        matchType | matchTypeField
        1         | "field_text__c"
        2         | "field_text__c"
    }


    def "test validateUniqueRule"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        String fieldApiName = "field_text__c"
        fieldDescribe.setApiName(fieldApiName)
        List<IFieldDescribe> validFieldList = Lists.newArrayList(fieldDescribe)

        Map<String, Object> data = Maps.newHashMap()
        data.put(fieldApiName, "test")


        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                matchingType: matchType
        )

        StandardUpdateImportVerifyAction action = new StandardUpdateImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                arg: arg
        )
        when:
        action.validateUniqueRule(validFieldList, data.entrySet())
        then:
        noExceptionThrown()
        where:
        matchType | matchTypeField
        1         | "field_text__c"
        3         | "field_text__c"
    }

    def "test verifyUniqueRule"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        String fieldApiName = "field_text__c"
        fieldDescribe.setApiName(fieldApiName)
        List<IFieldDescribe> validFieldList = Lists.newArrayList(fieldDescribe)

        IObjectData objectData = new ObjectData()
        objectData.set(fieldApiName, "test")

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                matchingType: matchType
        )

        StandardUpdateImportVerifyAction action = new StandardUpdateImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                arg: arg
        )
        when:
        action.verifyUniqueRule(validFieldList, objectData)
        then:
        noExceptionThrown()
        where:
        matchType | matchTypeField
        1         | "field_text__c"
        3         | "field_text__c"
    }
}

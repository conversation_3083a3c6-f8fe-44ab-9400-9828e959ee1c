package com.facishare.paas.appframework.core.predef.action

import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import spock.lang.Specification

class StandardImportDataAddActionGroovyTest extends Specification {

    def objectApiName = "object_123__c"

    def actionCode = "UnionInsertImportData"

    def tenantId = "74255"

    def userId = "1000"

    def outTenantId = "200074255"

    def outUserId = "100018916"

    def user = User.builder().tenantId(tenantId).userId(userId).outTenantId(outTenantId).outUserId(outUserId).build()

    def requestContext = RequestContext.builder().tenantId(tenantId).user(user).build()

    def actionContext = new ActionContext(requestContext, objectApiName, actionCode)

    def detailApiName = "object_detail__c"

    ServiceFacade serviceFacade

    def setup() {
        serviceFacade = Mock(ServiceFacade)
    }

    def "test customInit"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        Map<String, List<BaseImportDataAction.ImportData>> importDataListMap = Maps.newHashMap()
        List<BaseImportDataAction.ImportData> dataList = []
        BaseImportDataAction.ImportData data = new BaseImportDataAction.ImportData()
        data.setRowNo(1)
        data.setData(new ObjectData())
        dataList.add(data)
        importDataListMap.put(objectApiName, dataList)
        StandardImportDataAddAction action = new StandardImportDataAddAction(
                importDataListMap: importDataListMap,
                objectDescribe: objectDescribe,
                actionContext: actionContext
        )
        when:
        objectDescribe.isPublicObject() >> isPublic
        action.customInit(dataList)
        then:
        noExceptionThrown()
        where:
        isPublic | _
        true     | _
        false    | _
    }


    def "test customConvertLabelToApiName"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)

        BaseImportAction.MasterInfo masterInfo = new BaseImportAction.MasterInfo()
        masterInfo.setApiName(objectApiName)
        masterInfo.setData(createDataDocumentList())

        List<BaseImportAction.DetailInfo> detailInfoList = Lists.newArrayList()
        BaseImportAction.DetailInfo detailInfo = new BaseImportAction.DetailInfo()
        detailInfo.setApiName(detailApiName)
        detailInfo.setDataList(createDataDocumentList())
        detailInfoList.add(detailInfo)

        IObjectDescribe detailDescribe = Spy(ObjectDescribe)
        detailDescribe.setApiName(detailApiName)

        Map<String, IObjectDescribe> describeMap = Maps.newHashMap()
        describeMap.put(objectApiName, objectDescribe)
        describeMap.put(detailApiName, detailDescribe)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                masterInfo: masterInfo,
                detailInfo: detailInfoList
        )

        StandardImportDataAddAction action = new StandardImportDataAddAction(
                objectDescribe: objectDescribe,
                actionContext: actionContext,
                arg: arg,
                describeMap: describeMap
        )
        when:
        action.customConvertLabelToApiName(Lists.newArrayList(), objectDescribe)
        then:
        noExceptionThrown()
    }

    def createDataDocumentList() {
        List<ObjectDataDocument> result = Lists.newArrayList()
        for (int i = 2; i < 5; i++) {
            ObjectDataDocument document = ObjectDataDocument.of(
                    ["rowNo"       : i, "负责人（必填）": "admin01", "业务类型（必填）": "预设业务类型", "日期": "2019-10-16 00:00:00",
                     "主属性（必填）": "导入" + i, "数字": 100, "归属部门": ""])
            result.add(document)
        }
        return result
    }


    def "test initDescribeMap"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)

        IObjectDescribe detailObjectDescribe = Spy(ObjectDescribe)
        detailObjectDescribe.setApiName(detailApiName)

        List<BaseImportAction.DetailInfo> detailInfoList = Lists.newArrayList()
        BaseImportAction.DetailInfo detailInfo = new BaseImportAction.DetailInfo()
        detailInfo.setApiName(detailApiName)
        detailInfoList.add(detailInfo)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                detailInfo: detailInfoList
        )

        StandardImportDataAddAction action = new StandardImportDataAddAction(
                objectDescribe: detailObjectDescribe,
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                arg: arg
        )

        when:
        serviceFacade.findObjectsWithoutCopy(_, _) >> [detailApiName: detailObjectDescribe]
        action.initDescribeMap()
        then:
        noExceptionThrown()
    }

    def "test validateMultiLang"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        Map<String, List<BaseImportDataAction.ImportData>> importDataListMap = Maps.newHashMap()
        List<BaseImportDataAction.ImportData> dataList = Lists.newArrayList()
        BaseImportDataAction.ImportData data = new BaseImportDataAction.ImportData()
        data.setRowNo(1)
        data.setData(new ObjectData())
        dataList.add(data)
        importDataListMap.put(objectApiName, dataList)

        Map<String, IObjectDescribe> describeMap = Maps.newHashMap()
        describeMap.put(objectApiName, objectDescribe)

        StandardImportDataAddAction action = new StandardImportDataAddAction(
                importDataListMap: importDataListMap,
                objectDescribe: objectDescribe,
                actionContext: actionContext,
                describeMap: describeMap
        )
        when:
        action.validateMultiLang(dataList, objectDescribe)
        then:
        noExceptionThrown()
    }

    def "test convertFields"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(objectApiName, objectDescribe)
        Map<String, List<BaseImportDataAction.ImportData>> importDataListMap = Maps.newHashMap()
        List<BaseImportDataAction.ImportData> dataList = Lists.newArrayList()
        BaseImportDataAction.ImportData data = new BaseImportDataAction.ImportData()
        data.setRowNo(1)
        data.setData(new ObjectData())
        dataList.add(data)
        importDataListMap.put(objectApiName, dataList)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                matchingType: matchingType
        )
        StandardImportDataAddAction action = new StandardImportDataAddAction(
                importDataListMap: importDataListMap,
                describeMap: describeMap,
                objectDescribe: objectDescribe,
                actionContext: actionContext,
                arg: arg
        )
        when:
        action.convertFields(dataList, objectDescribe)
        then:
        noExceptionThrown()
        where:
        matchingType | _
        1            | _
        2            | _
    }

    def "test customFilterHeader"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(objectApiName, objectDescribe)

        Map<String, List<IFieldDescribe>> validFieldListMap = Maps.newHashMap()
        List<IFieldDescribe> fieldDescribeList = Lists.newArrayList()
        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        fieldDescribe.setApiName(fieldApiName)
        fieldDescribeList.add(fieldDescribe)

        objectDescribe.setFieldDescribes(fieldDescribeList)

        StandardImportDataAddAction action = new StandardImportDataAddAction(
                validFieldListMap: validFieldListMap,
                describeMap: describeMap,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                actionContext: actionContext
        )
        when:
        action.customFilterHeader(fieldApiName, objectDescribe)
        then:
        noExceptionThrown()
        where:
        fieldApiName    | _
        "field_text__c" | _
        ""              | _
    }

    def "test validateField"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(objectApiName, objectDescribe)
        Map<String, List<BaseImportDataAction.ImportData>> importDataListMap = Maps.newHashMap()
        List<BaseImportDataAction.ImportData> dataList = Lists.newArrayList()
        BaseImportDataAction.ImportData data = new BaseImportDataAction.ImportData()
        data.setRowNo(1)
        data.setData(new ObjectData())
        dataList.add(data)
        importDataListMap.put(objectApiName, dataList)
        StandardImportDataAddAction action = new StandardImportDataAddAction(
                importDataListMap: importDataListMap,
                describeMap: describeMap,
                objectDescribe: objectDescribe,
                actionContext: actionContext
        )
        when:
        action.validateField(objectDescribe, dataList)
        then:
        noExceptionThrown()
    }

    def "test generateResult"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(objectApiName, objectDescribe)
        Map<String, List<IObjectData>> actualDataListMap = Maps.newHashMap()
        List<IObjectData> dataList = Lists.newArrayList()
        dataList.add(new ObjectData())
        actualDataListMap.put(objectApiName, dataList)
        List<BaseImportAction.ImportError> allErrorList = Lists.newArrayList()
        StandardImportDataAddAction action = new StandardImportDataAddAction(
                actualDataListMap: actualDataListMap,
                describeMap: describeMap,
                objectDescribe: objectDescribe,
                actionContext: actionContext,
                allErrorList: allErrorList
        )
        when:
        action.generateResult(new BaseImportAction.Result())
        then:
        noExceptionThrown()
    }


    def "test mergeErrorList"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(objectApiName, objectDescribe)
        List<BaseImportAction.ImportError> allErrorList = Lists.newArrayList()
        StandardImportDataAddAction action = new StandardImportDataAddAction(
                objectDescribe: objectDescribe,
                actionContext: actionContext,
                allErrorList: allErrorList
        )
        when:
        action.mergeErrorList(allErrorList)
        then:
        noExceptionThrown()
    }

    def "test recordImportDataLog"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(objectApiName, objectDescribe)
        Map<String, List<IObjectData>> actualDataListMap = Maps.newHashMap()
        List<IObjectData> dataList = Lists.newArrayList()
        dataList.add(new ObjectData())
        actualDataListMap.put(objectApiName, dataList)
        StandardImportDataAddAction action = new StandardImportDataAddAction(
                actualDataListMap: actualDataListMap,
                describeMap: describeMap,
                objectDescribe: objectDescribe,
                actionContext: actionContext,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                serviceFacade: serviceFacade
        )
        when:
        action.recordImportDataLog(dataList)
        then:
        noExceptionThrown()
    }


    def "test importDataByAddAction"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)

        Map<String, List<BaseImportDataAction.ImportData>> importDataListMap = Maps.newHashMap()
        List<BaseImportDataAction.ImportData> dataList = []
        BaseImportDataAction.ImportData data = new BaseImportDataAction.ImportData()
        data.setRowNo(1)
        data.setData(new ObjectData())
        dataList.add(data)
        importDataListMap.put(objectApiName, dataList)
        importDataListMap.put(detailApiName, dataList)

        List<BaseImportAction.ImportError> errorList = Lists.newArrayList()
        BaseImportAction.ImportError importError = new BaseImportAction.ImportError()
        importError.setRowNo(1)
        importError.setObjectApiName(detailApiName)
        errorList.add(importError)

        BaseImportAction.MasterInfo masterInfo = new BaseImportAction.MasterInfo()
        masterInfo.setApiName(objectApiName)

        List<BaseImportAction.DetailInfo> detailInfoList = Lists.newArrayList()
        BaseImportAction.DetailInfo detailInfo = new BaseImportAction.DetailInfo()
        detailInfo.setApiName(detailApiName)
        detailInfoList.add(detailInfo)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                masterInfo: masterInfo,
                detailInfo: detailInfoList
        )

        ImportLogMessage.ImportMessageBuilder importLogMessageBuilder = ImportLogMessage.createAndStart(actionContext.getUser(), arg, actionContext.getActionCode());

        StandardImportDataAddAction action = new StandardImportDataAddAction(
                importDataListMap: importDataListMap,
                allErrorList: errorList,
                objectDescribe: objectDescribe,
                actionContext: actionContext,
                arg: arg,
                importLogMessageBuilder: importLogMessageBuilder
        )
        when:
        action.importDataByAddAction()
        then:
        noExceptionThrown()
    }

    def "test validateUniqueDataInDB"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)

        String fieldTextApiName = "field_text__c"
        IFieldDescribe fieldDescribe = new SelectOneFieldDescribe()
        fieldDescribe.setApiName(fieldTextApiName)
        fieldDescribe.setUnique(true)
        fieldDescribe.setEnableMultiLang(true)

        objectDescribe.setFieldDescribes(Lists.newArrayList(fieldDescribe))

        Map<String, List<BaseImportDataAction.ImportData>> importDataListMap = Maps.newHashMap()
        List<BaseImportDataAction.ImportData> dataList = []
        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData()
        importData.setRowNo(1)

        IObjectData data = new ObjectData()
        data.set(fieldTextApiName, "other")
        data.set(fieldTextApiName + "__o", "其他")
        importData.setData(data)
        dataList.add(importData)
        importDataListMap.put(objectApiName, dataList)

        BaseImportAction.MasterInfo masterInfo = new BaseImportAction.MasterInfo()
        masterInfo.setApiName(objectApiName)

        Map<String, IObjectDescribe> describeMap = Maps.newHashMap()
        describeMap.put(objectApiName, objectDescribe)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                masterInfo: masterInfo,
        )

        StandardImportDataAddAction action = new StandardImportDataAddAction(
                importDataListMap: importDataListMap,
                objectDescribe: objectDescribe,
                actionContext: actionContext,
                arg: arg,
                describeMap: describeMap,
                serviceFacade: serviceFacade
        )
        when:
        action.validateUniqueDataInDB()
        then:
        noExceptionThrown()
    }

    def "test validateUniqueDataInExcel"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)

        String fieldTextApiName = "field_text__c"
        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        fieldDescribe.setApiName(fieldTextApiName)
        fieldDescribe.setUnique(true)
        fieldDescribe.setEnableMultiLang(true)

        objectDescribe.setFieldDescribes(Lists.newArrayList(fieldDescribe))

        Map<String, List<BaseImportDataAction.ImportData>> importDataListMap = Maps.newHashMap()
        List<BaseImportDataAction.ImportData> dataList = []
        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData()
        IObjectData data = new ObjectData()
        data.set(fieldTextApiName, "text")
        importData.setRowNo(1)
        importData.setData(data)
        dataList.add(importData)

        BaseImportDataAction.ImportData importData2 = new BaseImportDataAction.ImportData()
        IObjectData data2 = new ObjectData()
        data2.set(fieldTextApiName, "text2")
        importData2.setRowNo(2)
        importData2.setData(data2)
        dataList.add(importData2)

        BaseImportDataAction.ImportData importData3 = new BaseImportDataAction.ImportData()
        IObjectData data3 = new ObjectData()
        data3.set(fieldTextApiName, "text2")
        importData3.setRowNo(3)
        importData3.setData(data3)
        dataList.add(importData3)

        importDataListMap.put(objectApiName, dataList)

        BaseImportAction.MasterInfo masterInfo = new BaseImportAction.MasterInfo()
        masterInfo.setApiName(objectApiName)

        Map<String, IObjectDescribe> describeMap = Maps.newHashMap()
        describeMap.put(objectApiName, objectDescribe)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                masterInfo: masterInfo,
        )

        StandardImportDataAddAction action = new StandardImportDataAddAction(
                importDataListMap: importDataListMap,
                objectDescribe: objectDescribe,
                actionContext: actionContext,
                arg: arg,
                describeMap: describeMap,
                serviceFacade: serviceFacade
        )
        when:
        action.validateUniqueDataInExcel()
        then:
        noExceptionThrown()
    }
}

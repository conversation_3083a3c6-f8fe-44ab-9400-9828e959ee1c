import com.facishare.paas.appframework.core.model.ActionContext
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.action.StandardBulkDeleteAction
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * Created by fengjy in 2019/11/26 11:59
 */
class StandardBulkDeleteActionGroovyTest extends Specification {
    def bulkDeleteAction = new StandardBulkDeleteAction(
            serviceFacade: Mock(ServiceFacade),
            actionContext: new ActionContext(RequestContext.builder()
                    .tenantId(tenantId)
                    .user(new User("testTenantId", User.SUPPER_ADMIN_USER_ID))
                    .build(), "testObjectApiName", "testBulkDelete"),
            objectDescribe: getObjectDescribe(),
            dataList: [getObjectData()]
    )

    String tenantId = "71698"
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    def setup() {
    }

    def "test before"() {
        expect:
        1==1
        /*given:
        def arg = getArg()
        bulkDeleteAction.actionContext.attributes.put(RequestContext.Attributes.SKIP_BASE_VALIDATE, Boolean.TRUE)
        bulkDeleteAction.actionContext.attributes.put(RequestContext.Attributes.SKIP_FUNCTION_ACTION, Boolean.TRUE)
        bulkDeleteAction.arg = arg
        when:
        bulkDeleteAction.before(arg)
        then:
        1 * bulkDeleteAction.serviceFacade.findObject(_, _) >> getObjectDescribe()
        1 * bulkDeleteAction.serviceFacade.batchGetApprovalStatusOfObject(_, _) >> [:]

        noExceptionThrown()*/
    }

    def "test doAct"() {
        expect:
        1==1
       /* when:
        bulkDeleteAction.doAct(arg)
        then:
        1 * bulkDeleteAction.serviceFacade.findDetailDescribes(_, _) >> [getObjectDescribe()]
        1 * bulkDeleteAction.serviceFacade.findDetailIncludeInvalidObjectDataListIgnoreFormula(_, _, _) >> [getObjectData()]
        2 * bulkDeleteAction.serviceFacade.bulkDelete(_, _) >> [getObjectData()]

        noExceptionThrown()*/
    }

    def "test after"() {
        given:
        bulkDeleteAction.allHandledDataList = [getObjectData()]
        bulkDeleteAction.actionListeners = []
        when:
        bulkDeleteAction.after(arg, new StandardBulkDeleteAction.Result.ResultBuilder().build())
        then:
        1 * bulkDeleteAction.serviceFacade.sendActionMq(_, _, _)
        noExceptionThrown()
    }

//    def "test validateObjectApprovalFlow"() {
//        given:
//        bulkDeleteAction.arg = getArg()
//        bulkDeleteAction.serviceFacade.batchGetApprovalStatusOfObject(_, _) >> ["testObjectId": ApprovalFlowStatus.of("in_progress")]
//        when:
//        bulkDeleteAction.validateObjectApprovalFlow()
//        then:
//        thrown(ValidateException)
//    }


    ObjectData getObjectData() {
        new ObjectData([
                "is_active"               : Boolean.TRUE,
                "api_name"                : "testAPIName",
                "_id"                     : "testObjectId",
                "name"                    : "testObjectName",
                "object_describe_api_name": "testDescribeAPIName"
        ])
    }

    ObjectDescribe getObjectDescribe() {
        new ObjectDescribe(["is_active": Boolean.TRUE, "api_name": "testDescribeAPIName"])
    }

    StandardBulkDeleteAction.Arg getArg() {
        new StandardBulkDeleteAction.Arg(
                idList: ["testId"],
                describeApiName: "testDescribeApiName"
        )
    }

}

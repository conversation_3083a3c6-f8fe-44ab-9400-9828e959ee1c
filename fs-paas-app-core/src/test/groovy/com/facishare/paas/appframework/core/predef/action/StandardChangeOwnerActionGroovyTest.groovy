package com.facishare.paas.appframework.core.predef.action

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.serializer.NameFilter
import com.alibaba.fastjson.serializer.SerializeConfig
import com.facishare.paas.appframework.common.util.JacksonUtils
import spock.lang.Specification

/**
 * create by <PERSON><PERSON><PERSON> on 2020/07/20
 */
class StandardChangeOwnerActionGroovyTest extends Specification {
    def "test arg"() {
        when:
        def arg = JacksonUtils.fromJson(json, StandardChangeOwnerAction.Arg)
        then:
        println arg
        arg.isCascadeDealDetail()
        where:
        json                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | _
        '''{"Data":[{"objectDataId":"5f0ea3d828214c00016c1ff1","ownerId":["1001"],"dataOwnDepartmentId":["1002"]}],
"oldOwnerStrategy":"1","oldOwnerTeamMemberRole":null,"oldOwnerTeamMemberRoleList":null,
"oldOwnerTeamMemberPermissionType":"2","isCascadeDealDetail":true,"skipTriggerApprovalFlow":true,"relateObjectApiNames":null,"isUpdateDataOwnDepartment":true,"isNoSepcSpu":false,"skipPreAction":false,"skipButtonConditions":false,"isFlowCallBack":false}'''                                                                                                                                                                                                                                                                                                                                                                   | _
        '''{"oldOwnerStrategy":"1","oldOwnerTeamMemberPermissionType":"2","skipTriggerApprovalFlow":true,"skipPreAction":false,"skipButtonConditions":false,"noSepcSpu":false,"flowCallBack":false,"cascadeDealDetail":true,"updateDataOwnDepartment":true,"dataIds":["5f1a5980dce688000103344d","5f1a5980dce688000103344c"],"Data":[{"objectDataId":"5f1a5980dce688000103344d","ownerId":["2483"],"dataOwnDepartmentId":["1087"]},{"objectDataId":"5f1a5980dce688000103344c","ownerId":["2483"],"dataOwnDepartmentId":["1087"]}],"isCascadeDealDetail":true,"isUpdateDataOwnDepartment":true,"isNoSepcSpu":false,"isFlowCallBack":false}''' | _
    }

    def "test arg1"() {
        when:
        StandardChangeOwnerAction.Arg arg = new StandardChangeOwnerAction.Arg()
        arg.setNoSepcSpu(true)
        then:
//        String argJsonStr = JSON.toJSONString(arg)
        String argJsonStr = getArgJsonString(arg)
        println(argJsonStr)
        StandardChangeOwnerAction.Arg newArg = JacksonUtils.fromJson(argJsonStr, StandardChangeOwnerAction.Arg)
//        StandardChangeOwnerAction.Arg newArg = JSON.parseObject(argJsonStr, StandardChangeOwnerAction.Arg)
        println(newArg.isNoSepcSpu())
        newArg.isNoSepcSpu() == arg.isNoSepcSpu()
    }

    private String getArgJsonString(StandardChangeOwnerAction.Arg spuChangeOwnerArg) {
        NameFilter nameFilter = new NameFilter() {
            String process(Object o, String s, Object o1) {
                if ("data".equals(s)) {
                    return s.substring(0, 1).toUpperCase()
                            .concat(s.substring(1).toLowerCase());
                } else {
                    return s;
                }
            };
        }

        SerializeConfig serializeConfig = new SerializeConfig();
        serializeConfig.addFilter(StandardChangeOwnerAction.Arg.class, nameFilter);
        return JSON.toJSONString(spuChangeOwnerArg, serializeConfig);
    }

    def "test arg2"() {
        when:
        String jsonStr = "{\"cascadeDealDetail\":false,\"Data\":[{\"objectDataId\":\"601cbb7b9792540001e9b544\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b545\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b546\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b547\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b548\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b549\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b54a\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b54b\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b54c\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b54d\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b54e\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b54f\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b550\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b551\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b552\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b553\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b554\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b555\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b556\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b557\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b558\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b559\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b55a\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b55b\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b55c\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b55d\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b55e\",\"ownerId\":[\"1000\"]},{\"objectDataId\":\"601cbb7b9792540001e9b55f\",\"ownerId\":[\"1000\"]}],\"flowCallBack\":false,\"noSepcSpu\":true,\"oldOwnerStrategy\":\"1\",\"oldOwnerTeamMemberPermissionType\":\"2\",\"oldOwnerTeamMemberRoleList\":[\"4\"],\"relateObjectApiNames\":[],\"skipButtonConditions\":false,\"skipPreAction\":false,\"skipTriggerApprovalFlow\":true,\"updateDataOwnDepartment\":true}"
        Class<StandardChangeOwnerAction.Arg> clazz = StandardChangeOwnerAction.Arg
        then:
        StandardChangeOwnerAction.Arg newArg = JacksonUtils.fromJson(jsonStr, clazz)
        println(newArg)
        newArg.isNoSepcSpu()
    }

}

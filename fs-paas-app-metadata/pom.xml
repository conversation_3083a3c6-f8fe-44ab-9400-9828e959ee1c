<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-paas-appframework</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.6.5-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>fs-paas-app-metadata</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-metadata-util</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-metadata-dao</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-license</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-privilege</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-log</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-flow</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-config</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-prm</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-function</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>fs-redisson-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fsi-proxy</artifactId>
            <version>${fs-fsi-proxy.version}</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-idempotent-util</artifactId>
        </dependency>

        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>xml-apis</artifactId>
                    <groupId>xml-apis</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>jaxen</groupId>
            <artifactId>jaxen</artifactId>
            <version>1.1.6</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-function-service-api</artifactId>
            <version>${fs-paas-function.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-metadata-service</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-paas-app-metadata-restdriver</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.powermock</groupId>
                    <artifactId>powermock-api-mockito2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.powermock</groupId>
                    <artifactId>powermock-api-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.powermock</groupId>
                    <artifactId>powermock-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>metrics-oss</artifactId>
        </dependency>

        <!--
        move to fs-paas-app-common/pom.xml
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fsc-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        -->

        <!-- OCR client -->
        <dependency>
            <artifactId>fs-paas-wishful-ocr-api</artifactId>
            <groupId>com.facishare</groupId>
            <version>1.1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-metadata-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-recording-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!-- 企信菜单配置 -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-webpage-customer-api</artifactId>
            <version>1.1.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-metadata-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-qixin-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-dubbo-rest-plugin</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <groupId>javax.ws.rs</groupId>
                    <artifactId>javax.ws.rs-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-timezone-core</artifactId>
        </dependency>

        <!--        引入社交服务-->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-social-api</artifactId>
        </dependency>

        <!--        文件服务授权能力-->
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-stone-commons-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-task-async</artifactId>
        </dependency>

    </dependencies>
</project>